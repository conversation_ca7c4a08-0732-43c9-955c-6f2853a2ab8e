import datetime
from re import T
from django.db import models
from django.utils import timezone
#from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from common.exception import WmsException
from common.translation import _T
from AccessGateway.models import C<PERSON><PERSON>User
from AccessGateway.models import LedgerModel
from django_multitenant.fields import TenantForeignKey;
from django_multitenant.models import TenantModel




class ZTUser(LedgerModel):
    """租户用户表"""
    class RoleTypeChoices(models.TextChoices):
        ADMIN = 'admin', '管理员'
        OPERATOR = 'operator', '操作员'
        PURCHASER = 'purchaser', '采购员'
        SALES = 'sales', '销售员'
        STOCK_MANAGER = 'stock_manager', '仓库管理员'
        FINANCE = 'finance', '财务员'

    user_id = models.IntegerField(verbose_name='用户ID')
    name = models.CharField(max_length=100, verbose_name='用户名称')
    #role = models.Char<PERSON>ield(max_length=20, choices=RoleTypeChoices.choices, verbose_name='角色')
    roles = models.JSONField(default=list, verbose_name='角色列表')

    class Meta:
        verbose_name = '租户用户'
        verbose_name_plural = '租户用户'
        unique_together = ('id', 'ledger'), ('user_id', 'ledger')

class CatalogTypeTree(LedgerModel):
    """通用分类树表"""
    class ForTypeChoices(models.TextChoices):
        SUPPLIER = 'supplier', '供应商'
        CUSTOMER = 'customer', '客户'
        ITEM = 'item', '物品'

    name = models.CharField(max_length=100, verbose_name='分类名称')
    parent = TenantForeignKey('self', on_delete=models.PROTECT, verbose_name='父分类', null=True, blank=True)
    for_type = models.CharField(max_length=100, verbose_name='类型', choices=ForTypeChoices, default=ForTypeChoices.ITEM)
    
    class Meta:
        verbose_name = '分类'
        unique_together = ('id', 'ledger')
        
    def __str__(self):
        return f"{self.get_for_type_display()}-{self.name}"

class Warehouse(LedgerModel):
    """仓库表"""
    name = models.CharField(max_length=100, verbose_name='仓库名称', error_messages={'unique': _T('仓库名称已存在')})
    location = models.CharField(max_length=200, verbose_name='仓库所在地', blank=True, null=True)
    is_parent = models.BooleanField(default=False, verbose_name='是否父仓')
    #parent = models.ForeignKey('self', on_delete=models.PROTECT, verbose_name='父类型', null=True, blank=True)
    parent = TenantForeignKey('self', on_delete=models.PROTECT, verbose_name='父类型', null=True, blank=True)

    class Meta:
        verbose_name = '仓库'
        verbose_name_plural = '仓库'
        unique_together = ('id', 'ledger'), ('name', 'ledger')

    def __str__(self):
        return f"{self.name}-{self.location}"


class Customer(LedgerModel):
    """客户表"""
    name = models.CharField(max_length=100, verbose_name='客户名称')
    contact_person = models.CharField(max_length=50, verbose_name='联系人')
    phone = models.CharField(max_length=20, verbose_name='联系电话', null=True, blank=True)
                           
    address = models.CharField(max_length=200, verbose_name='联系地址')
    customer_type = TenantForeignKey(CatalogTypeTree, on_delete=models.PROTECT, verbose_name='客户类型', null=True, blank=True)
    create_date = models.DateField(auto_now=True, verbose_name='建档日期')
    last_contact_date = models.DateField(null=True, blank=True, verbose_name='联络日期')
    last_purchase_date = models.DateField(null=True, blank=True, verbose_name='最近购买时间')

    class Meta:
        verbose_name = '客户'
        verbose_name_plural = '客户'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return self.name


class UnitType(LedgerModel):
    """计量单位类型表"""
    id = models.AutoField(primary_key=True)
    # 名称唯一，不能重复
    name = models.CharField(max_length=50, verbose_name='计量名称')

    class Meta:
        verbose_name = '计量单位类型'
        unique_together = ('id', 'ledger'), ('name', 'ledger')

class Unit(LedgerModel):
    """计量单位表"""
    unit_type = TenantForeignKey(UnitType, on_delete=models.PROTECT, verbose_name='计量单位类型', related_name='units_of_type')
    item = TenantForeignKey('Item', null=False, on_delete=models.CASCADE, verbose_name='关联物品', related_name='units')
    conversion_rate = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='兑换比例')
    retail_price = models.DecimalField(max_digits=30, default=0, decimal_places=8, verbose_name='零售价格')
    wholesale_price = models.DecimalField(max_digits=30, default=0, decimal_places=8, verbose_name='批发价格')
    price_strategy = models.JSONField(default=list, verbose_name='价格策略组')
    # order_cycle = models.IntegerField(default=10, verbose_name='订货周期')
    min_price = models.DecimalField(max_digits=30, default=0, decimal_places=8, verbose_name='最低价格')

    class Meta:
        verbose_name = '计量单位'
        verbose_name_plural = '计量单位'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.item.name} - {self.unit_type.name}"

class Item(LedgerModel):
    """物品表"""
    id = models.AutoField(primary_key=True)  # 自增 id 不能用default=  TODO 待完善从100000开始，现在是通过sql命令处理，我是能用
    code = models.CharField(max_length=50, verbose_name='物品编号', error_messages={'unique': _T('商品编码已被使用')})
    name = models.CharField(max_length=100, verbose_name='物品名称', error_messages={'unique': _T('商品名称已被使用')})
    item_type = TenantForeignKey(CatalogTypeTree, on_delete=models.PROTECT, verbose_name='物品类型' )
    total_stock = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='库存总量')
    total_cost = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='成本价')  # 这个就是成本价，目前使用最近入库成本
    # 临期或者过期商品数量
    expiry_quantity = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='临期或者过期商品数量')

    unit = TenantForeignKey("Unit", null=True, blank=True, on_delete=models.PROTECT, verbose_name='主计量单位', related_name='items')
    order_demand = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='订单需求量')
    order_lock = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='订单锁定量')
    purchase_quantity = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='采购量')
    reorder_cycle = models.IntegerField(default=10, verbose_name='订货周期')
    expiry_days = models.IntegerField(default=0, verbose_name='有效期/保质期')
    over_sell_quantity = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已超卖数量')  # 可能二选一
    is_over_sell = models.BooleanField(default=False, verbose_name='是否支持超卖')
    is_active = models.BooleanField(default=True, verbose_name='是否有效')
    serial_number = models.CharField(max_length=100, null=True, blank=True, verbose_name='序列号')
    color = models.CharField(max_length=50, null=True, blank=True, verbose_name='颜色')
    brand = models.CharField(max_length=100, null=True, blank=True, verbose_name='品牌')
    model = models.CharField(max_length=100, null=True, blank=True, verbose_name='型号')
    base_weight = models.DecimalField(max_digits=30, decimal_places=8, null=True, blank=True, verbose_name='基础重量(kg)')
    location = models.CharField(max_length=100, null=True, blank=True, verbose_name='仓位货架')
    manufacturer = models.CharField(max_length=100, null=True, blank=True, verbose_name='制造商')
    specification = models.CharField(max_length=200, null=True, blank=True, verbose_name='规格')
    images = models.JSONField(default=list, verbose_name='图片列表') #'[filename1, filename2, ...]'

    class Meta:
        verbose_name = '物品'
        verbose_name_plural = '物品'
        unique_together = ('id', 'ledger'), ('code', 'ledger'), ('name', 'ledger'), ('serial_number', 'ledger')

    def __str__(self):
        return self.name

    def clean(self):
        try:
            super().clean()
        except ValidationError as e:
            if hasattr(e, 'message_dict'):
                field, messages = list(e.message_dict.items())[0]
                raw_msg = messages[0]
                # 尝试用字段值格式化
                value = getattr(self, field, '')
                try:
                    msg = _T(raw_msg, value=value)
                except Exception:
                    msg = _T(raw_msg)
                raise WmsException({'code': 1001, 'msg': msg, 'field': field})
            raise WmsException({'code': 1001, 'msg': _T(str(e))})

class PaymentMethod(LedgerModel):
    """付款方式表"""

    class AccountTypeChoices(models.TextChoices):
        CASH = 'cash', '现金'
        ALIPAY = 'alipay', '支付宝'
        WECHAT = 'wechat', '微信'
        BANK = 'bank', '银行'
        OTHER = 'other', '其他'

    class SettlementTypeChoices(models.TextChoices):
        CASH = 'cash', '现金'
        WECHAT = 'wechat', '微信'
        ALIPAY = 'alipay', '支付宝'
        CREDIT_CARD = 'credit_card', '信用卡'
        BANK_DRAFT = 'bank_draft', '银行汇票'
        BANK_NOTE = 'bank_note', '银行本票'
        CHECK = 'check', '支票'

    id = models.AutoField(primary_key=True)
    account_type = models.CharField(max_length=20, choices=AccountTypeChoices.choices, verbose_name='账户类型')
    account_name = models.CharField(max_length=100, verbose_name='账户名称')
    bank_name = models.CharField(max_length=100, verbose_name='开户银行', null=True, blank=True)
    bank_account = models.CharField(max_length=100, verbose_name='银行账户', null=True, blank=True)
    bank_account_name = models.CharField(max_length=100, verbose_name='银行账户名称', null=True, blank=True)
    current_balance = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='最新余额')
    default_settlement = models.CharField(max_length=20, choices=SettlementTypeChoices.choices, verbose_name='默认结算方式')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '付款方式'
        verbose_name_plural = '付款方式'
        ordering = ['account_type', 'account_name']
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.get_account_type_display()}-{self.account_name}"


class Stock(LedgerModel):
    """库存表"""
    class StockInChoices(models.TextChoices):
        PURCHASE_IN = 'purchase_in', '采购入库'
        RETURN = 'return', '退货'
        ADJUSTMENT = 'adjustment', '调拨'
        INVENTORY = 'inventory', '盘点'
        PRODUCTION = 'production', '生产'
        ASSEMBLY = 'assembly', '组装'
        INITIAL_IN = 'initial_in', '初始入库'
        OTHER = 'other', '其他'

    id = models.AutoField(primary_key=True)
    item = TenantForeignKey(Item, on_delete=models.CASCADE, verbose_name='物品')
    warehouse = TenantForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name='所在仓库')

    unit = TenantForeignKey(Unit, on_delete=models.PROTECT, verbose_name='存库中的计量单位')
    in_type = models.CharField(max_length=20, choices=StockInChoices, default='purchase_in', verbose_name='入库类型')
    in_order_id = models.CharField(max_length=50, verbose_name='入库单号', null=True, blank=True)
    # 批次号字段需要建立索引
    batch_number = models.CharField(max_length=50, verbose_name='批次号' ) #建立索引 
    # 第一次入库的商品才需要新建
    # 同个仓库中，同个track_id的商品，只会有一个库存表
    track_id = models.IntegerField(verbose_name='跟踪ID')
    in_date = models.DateField(auto_now=True, verbose_name='入库日期')
    expiry_days = models.IntegerField(default=0, verbose_name='保质期/有效期')  # 可能二选一
    actual_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='实际成本')  # 入库成本
    allocated_cost = models.DecimalField(max_digits=30, decimal_places=8, null=True, verbose_name='费用分摊成本')  # 附加费，比如运费
    out_cost = models.DecimalField(max_digits=30, decimal_places=8, null=True, verbose_name='出库成本')  # 移动平均那种，就是由结果反推
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='库存数量')  # 一开始的数量
    remaining_quantity = models.DecimalField(max_digits=30, decimal_places=8, null=True, verbose_name='剩余待出库量')  # 现在剩下的实际数量

    class Meta:
        verbose_name = '库存'
        verbose_name_plural = '库存'
        indexes = [
            models.Index(fields=['batch_number'], name='stock_batch_number_idx'),
            models.Index(fields=['track_id'], name='stock_track_id_idx'),
        ]
        #ordering = ['-in_date', 'batch_number']
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.item.name} - {self.warehouse.name}"


class SalesOrder(LedgerModel):
    """销售订单表"""

    class OrderStatusChoices(models.TextChoices):
        PENDING = 'pending', _T('预定单')
        PARTIAL = 'partial', _T('部分出库')
        COMPLETED = 'completed', _T('全部出库')
        CANCELLED = 'cancelled', _T('已取消')

    class PaymentStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', _T('未付款')
        PARTIAL = 'partial', _T('部分付款')
        PAID = 'paid', _T('全部付款')

    id = models.AutoField(primary_key=True)
    order_id = models.CharField(max_length=50, verbose_name='订单编号')
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT, verbose_name='客户')
    order_date = models.DateTimeField(default=timezone.now, verbose_name='订单时间')
    item_count = models.IntegerField(default=0, verbose_name='子列表数量')
    total_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='订单金额')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='订单折扣数值')
    #预收款
    prepayment = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='预收款')
    # 已抵扣金额
    deduction = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已抵扣金额')
    expected_delivery_date = models.DateField(verbose_name='预计交货日期',null=True)
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='sales_order_returns_handler')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    is_draft = models.BooleanField(default=False, verbose_name='是否草稿')
    order_status = models.CharField(max_length=20, choices=OrderStatusChoices, default=OrderStatusChoices.PENDING, verbose_name='订单状态')
    payment_status = models.CharField(max_length=20, choices=PaymentStatusChoices, default=PaymentStatusChoices.UNPAID, verbose_name='付款状态')
    controller = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='操作者', related_name='sales_order_returns_controller')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, verbose_name='付款方式',null=True,blank=True)
    class Meta:
        verbose_name = '销售订单'
        verbose_name_plural = '销售订单'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"SO-{self.id}"

    def clean(self):
        if not self.customer or not Customer.objects.filter(pk=self.customer_id).exists():
            raise ValidationError({'customer': '客户不存在，请重新选择客户'})
        # 其它校验...


class SalesOrderItem(LedgerModel):
    """销售订单物品表"""
    id = models.AutoField(primary_key=True)
    sales_order = TenantForeignKey(SalesOrder, on_delete=models.CASCADE, verbose_name='销售订单', related_name='items')
    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    out_quantity = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已出库数量')
    sale_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='销售价格')  # 报价
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='折扣数值')
    unit = TenantForeignKey('Unit', on_delete=models.PROTECT, verbose_name='计量单位')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    class Meta:
        verbose_name = '销售订单物品'
        verbose_name_plural = '销售订单物品'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.sales_order} - {self.item.name}"


class SalesOut(LedgerModel):
    """销售出库表"""
    DOCUMENT_TYPE_CHOICES = (
        ('retail', '零售单'),
        ('wholesale', '批发单'),
    )
    document_type = models.CharField(max_length=10, choices=DOCUMENT_TYPE_CHOICES, default='retail', verbose_name='单据类型')
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT, verbose_name='客户')
    # 订单编号
    order_no = models.CharField(max_length=50, verbose_name='订单编号')
    id = models.AutoField(primary_key=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    sales_order = TenantForeignKey(SalesOrder, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='销售订单')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='sales_outs_handler')
    out_date = models.DateField(default=timezone.now, verbose_name='出库日期')
    warehouse = TenantForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name='出库仓库')
    total_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总出库成本')
    total_sale_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总售价')
    remark = models.TextField(verbose_name='备注', blank=True, null=True)
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='优惠金额')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    is_draft = models.BooleanField(default=False, verbose_name='是否暂存')
    controller = TenantForeignKey(ZTUser,on_delete=models.PROTECT,verbose_name='操作者',related_name='sales_outs_controller')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, verbose_name='付款方式',null=True,blank=True)
    class Meta:
        verbose_name = '销售出库'
        verbose_name_plural = '销售出库'
        unique_together = ('id', 'ledger')
    
    def __str__(self):
        return f"SO-{self.id}"


class SalesOutItem(LedgerModel):
    """销售出库物品表"""
    id = models.AutoField(primary_key=True)
    sales_out = TenantForeignKey(SalesOut, on_delete=models.CASCADE, verbose_name='销售出库单')
    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    out_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='出库成本')
    final_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='最终成本')
    sale_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='销售价格')
    unit=TenantForeignKey('Unit', on_delete=models.PROTECT, verbose_name='计量单位')
    class Meta:
        verbose_name = '销售出库物品'
        verbose_name_plural = '销售出库物品'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.sales_out} - {self.item.name}"

# 销售退货订单
class SalesReturn(LedgerModel):
    """销售退货订单"""
    # 订单类型
    ORDER_TYPE_CHOICES = (
        ('retail', '零售单'),
        ('wholesale', '批发单'),
    )
    # 订单状态
    class OrderStatusChoices(models.TextChoices):
        PENDING = 'pending', _T('预定单')
        PARTIAL = 'partial', _T('部分出库')
        COMPLETED = 'completed', _T('全部出库')
        CANCELLED = 'cancelled', _T('已取消')

    class PaymentStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', _T('未付款')
        PARTIAL = 'partial', _T('部分付款')
        PAID = 'paid', _T('全部付款')
    order_type = models.CharField(max_length=10, choices=ORDER_TYPE_CHOICES, default='wholesale', verbose_name='订单类型')
    # 编号
    order_no = models.CharField(max_length=50, verbose_name='编号')
    id = models.AutoField(primary_key=True)
    sales_out = TenantForeignKey(SalesOut, on_delete=models.SET_NULL, null=True, blank=True,
                                    verbose_name='销售出库单')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='sales_returns_handler')
    return_date = models.DateField(default=timezone.now, verbose_name='退货日期')
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT, verbose_name='客户')
    warehouse = TenantForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name='退货仓库')
    total_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总退货成本')
    total_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总价格')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='优惠金额')
    is_draft = models.BooleanField(default=False, verbose_name='是否暂存')
    order_status = models.CharField(max_length=20, choices=OrderStatusChoices.choices, default=OrderStatusChoices.PENDING, verbose_name='订单状态')
    payment_status = models.CharField(max_length=20, choices=PaymentStatusChoices.choices, default=PaymentStatusChoices.UNPAID, verbose_name='支付状态')
    controller = TenantForeignKey(ZTUser,on_delete=models.PROTECT,verbose_name='操作者',related_name='sales_returns_controller')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, verbose_name='付款方式',null=True,blank=True)
    # 物品种类数量
    item_count = models.IntegerField(default=0, verbose_name='物品种类数量')
    class Meta:
        verbose_name = '销售退货订单'
        verbose_name_plural = '销售退货订单'
        unique_together = ('id', 'ledger')
    # 验证销售出库单必须不是暂存状态
    def clean(self):
        super().clean()
        if self.sales_out and self.sales_out.is_draft:
            raise ValidationError({
                'sales_out': _T('销售出库单不能是暂存状态')
            })
    def __str__(self):
        return f"SR-{self.id}"


# 销售退货物品记录表
class SalesReturnItem(LedgerModel):
    """销售退货物品表"""
    id = models.AutoField(primary_key=True)
    sales_return = TenantForeignKey(SalesReturn, on_delete=models.CASCADE, verbose_name='销售退货订单')
    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='退货单价')
    final_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='最终成本')
    unit=TenantForeignKey('Unit', on_delete=models.PROTECT, verbose_name='计量单位')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    controller = TenantForeignKey(ZTUser,on_delete=models.PROTECT,verbose_name='操作者',related_name='sales_return_items_controller')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='sales_return_items_handler')

    class Meta:
        verbose_name = '销售退货物品'
        verbose_name_plural = '销售退货物品'
        unique_together = ('id', 'ledger')

# TODO 弃用收款表，改为应收 单
class Receipt(LedgerModel):
    """收款单"""

    class ReceiptStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', '未收款'
        PARTIAL = 'partial', '部分收款'
        FULL = 'full', '已收款'
        CANCELED = 'canceled', '已取消'

    class ReceiptOrderTypeChoices(models.TextChoices):
        SALES_ORDER = 'sales_order', '销售订单'
        RETAIL_ORDER = 'retail_order', '零售订单'

        
    id = models.AutoField(primary_key=True)
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT, verbose_name='客户')
    order_id = models.CharField(max_length=50, verbose_name='关联单号')
    order_type = models.CharField(max_length=20, choices=ReceiptOrderTypeChoices, verbose_name='关联单类型', default=ReceiptOrderTypeChoices.SALES_ORDER)
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='付款金额')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='receipts_handler')
    payment_date = models.DateTimeField(default=timezone.now, verbose_name='付款时间')
    received_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已收款金额')
    receipt_status = models.CharField(max_length=20, choices=ReceiptStatusChoices, default=ReceiptStatusChoices.UNPAID, verbose_name='收款状态')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='优惠减免')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    controller = TenantForeignKey(ZTUser,on_delete=models.PROTECT,verbose_name='操作者',related_name='receipts_controller')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    class Meta:
        verbose_name = '收款单'
        verbose_name_plural = '收款单'
        unique_together = ('id', 'ledger')

    def clean(self):
        super().clean()
        if self.receipt_status == self.ReceiptStatusChoices.FULL:
            expected_amount = self.amount - self.discount
            if self.received_amount != expected_amount:
                raise ValidationError({
                    'received_amount': _T('当收款状态为已收款时，已收款金额必须等于付款金额减去优惠减免')
                })

    def __str__(self):
        return f"RE-{self.id}"
    
class ReceiptRecord(LedgerModel):
    """收款记录"""
    id = models.AutoField(primary_key=True)
    # receipt = TenantForeignKey('Receipt', on_delete=models.PROTECT, verbose_name='收款单') #  弃用改为应收表
    accounts_receivable=TenantForeignKey('AccountsReceivable', on_delete=models.PROTECT, verbose_name='应收单')
    payment_method = TenantForeignKey('PaymentMethod', on_delete=models.PROTECT, verbose_name='支付方式')
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='收款金额')
    payment_date = models.DateTimeField(default=timezone.now, verbose_name='收款时间')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='receipts_record_handler')
    controller = TenantForeignKey(ZTUser,on_delete=models.PROTECT,verbose_name='操作者',related_name='receipts_record_controller')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '收款记录'
        verbose_name_plural = '收款记录'
        unique_together = ('id', 'ledger')
    def __str__(self):
        return f"RR-{self.id}"

# TODO 弃用退货付款单
class SalesReturnPayment(LedgerModel):
    id = models.AutoField(primary_key=True)
    sales_return = TenantForeignKey(SalesReturn, on_delete=models.PROTECT, verbose_name='销售退货订单')
    payment_method = TenantForeignKey('PaymentMethod', on_delete=models.PROTECT, verbose_name='支付方式')
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT, verbose_name='客户')
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='付款金额')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='sales_return_payments_handler')
    payment_date = models.DateTimeField(default=timezone.now, verbose_name='付款时间')
    received_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已收款金额')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='优惠减免')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    controller = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='操作者', related_name='sales_return_payments_controller')
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '退货付款单'
        verbose_name_plural = '退货付款单'
        unique_together = ('id', 'ledger')
    
    def __str__(self):
        return f"SRP-{self.id}"

    # 验证销售退货单不能是暂存状态
    def clean(self):
        super().clean()
        if self.sales_return.is_draft:
            raise ValidationError({
                'sales_return': _T('销售退货订单不能是暂存状态')
            })


class Payment(LedgerModel):
    """付款单"""
    class OrderTypeChoices(models.TextChoices):
        PURCHASE_ORDER = 'purchase_order', '采购订单'  #预付款
        PURCHASE_IN = 'purchase_in', '采购入库'
        COST_ALLOCATION = 'cost_allocation', '费用分摊'
        STOCK_RETURN = 'stock_return', '库存退货'
        SalesReturn = 'sales_return', '销售退货'

    id = models.AutoField(primary_key=True)
    supplier = TenantForeignKey('Supplier', on_delete=models.PROTECT,null=True, verbose_name='供应商')
    customer = TenantForeignKey(Customer, on_delete=models.PROTECT,null=True, verbose_name='客户')
    order_type =  models.CharField(max_length=20, choices=OrderTypeChoices, default=OrderTypeChoices.PURCHASE_ORDER, verbose_name='订单类型')
    order_id = models.CharField(max_length=50, verbose_name='订单编号')
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='付款金额')
    actual_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际付款金额')
    deposit_deduction = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='预付款冲抵金额')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='现金折扣')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='payments_handler')
    payment_date = models.DateField(default=timezone.now, verbose_name='付款日期')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, verbose_name='付款方式')
    controller = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='操作者', related_name='payments_controller')
    class Meta:
        verbose_name = '付款单'
        verbose_name_plural = '付款单'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"PV-{self.id}"

class Supplier(LedgerModel):
    """供应商表"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, verbose_name='客户名称')
    contact_person = models.CharField(max_length=50, null=True, blank=True, verbose_name='联系人')
    phone = models.CharField(max_length=20, null=True, blank=True, verbose_name='联系电话')
    address = models.CharField(max_length=200, null=True, blank=True, verbose_name='联系地址')
    supplier_type = TenantForeignKey('CatalogTypeTree', on_delete=models.PROTECT, verbose_name='供应商类型', null=True, blank=True)
    create_date = models.DateField(auto_now=True, verbose_name='建档日期')
    last_contact_date = models.DateField(null=True, blank=True, verbose_name='联络日期')
    last_supply_date = models.DateField(null=True, blank=True, verbose_name='最近供货日期')

    class Meta:
        verbose_name = '供应商'
        verbose_name_plural = '供应商'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return self.name


class PurchaseOrder(LedgerModel):
    """采购订单"""

    class OrderStatusChoices(models.TextChoices):
        PENDING = 'pending', '预定单'
        PARTIAL = 'partial', '部分交付'
        COMPLETED = 'completed', '全部交付'

    class PaymentStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', '未付款'
        PREPAID = 'prepaid', '已预付款'

    id = models.AutoField(primary_key=True)
    order_id = models.CharField(max_length=50, verbose_name='订单编号', default='')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    supplier = TenantForeignKey(Supplier, on_delete=models.PROTECT, verbose_name='供应商')
    order_date = models.DateField(auto_now=True, verbose_name='订单日期')
    expected_arrival_date = models.DateField(null=True, blank=True, verbose_name='预期到货日期')
    item_count = models.IntegerField(default=0, verbose_name='子列表数量')
    total_actual_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='实际采购金额')
    total_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='采购金额')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='purchase_orders_handler')
    order_status = models.CharField(max_length=20, choices=OrderStatusChoices, default='pending', verbose_name='订单状态')
    payment_status = models.CharField(max_length=20, choices=PaymentStatusChoices, default='unpaid', verbose_name='付款状态')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='现金折扣')
    deposit = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='预付款')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, null=True, blank=True, verbose_name='付款方式' )
    pay_amount = models.DecimalField(max_digits=30, decimal_places=2, default=0, verbose_name='已付款金额')
    #未被抵扣的预付款
    undeducted_deposit = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='未被抵扣的预付款')
    deposit_deduction = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已抵扣预付款')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '采购订单'
        verbose_name_plural = '采购订单'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.order_id}"


class PurchaseOrderItem(LedgerModel):
    """采购物品表"""
    id = models.AutoField(primary_key=True)
    purchase_order = TenantForeignKey(PurchaseOrder, on_delete=models.CASCADE, verbose_name='采购订单',
                                       related_name='items')
    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')
    unit = TenantForeignKey(Unit, on_delete=models.PROTECT, verbose_name='计量单位')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    delivered_quantity = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已交付数量')
    purchase_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='采购价格')
    actual_purchase_price = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际采购价')
    discount_rate = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='优惠折扣率(1-x)')
    allocated_cost = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='均摊费用')

    class Meta:
        verbose_name = '采购物品'
        verbose_name_plural = '采购物品'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.purchase_order} - {self.item.name}"


class PurchaseIn(LedgerModel):
    """采购入库表"""

    class InTypeChoices(models.TextChoices):
        PURCHASE_IN = 'purchase_in', '采购入库'
        RETURN_IN = 'return_in', '退货入库'
        ADJUSTMENT_IN = 'adjustment_in', '调拨入库'
        INITIAL_IN = 'initial_in', '期初入库'
        ASSEMBLY_IN = 'assembly_in', '组装入库'
        OTHER_IN = 'other_in', '其他入库'

    class InStatusChoices(models.TextChoices):
        DRAFT = 'draft', '暂存'
        COMPLETED = 'completed', '完成'
        CANCELLED = 'cancelled', '取消'

    class AllocationMethodChoices(models.TextChoices):
        AMOUNT = 'amount', '金额分摊'
        QUANTITY = 'quantity', '数量分摊'

    id = models.AutoField(primary_key=True)
    in_type = models.CharField(max_length=50, choices=InTypeChoices, default='purchase_in', verbose_name='入库类型')
    in_status = models.CharField(max_length=20, choices=InStatusChoices, default='draft', verbose_name='入库状态')
    purchase_order = TenantForeignKey(PurchaseOrder, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='采购订单')
    supplier = TenantForeignKey(Supplier, on_delete=models.PROTECT, verbose_name='供应商')
    order_id = models.CharField(max_length=50, verbose_name='订单编号')
    batch_number = models.CharField(max_length=50, verbose_name='入库批次号')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='purchase_ins_handler')
    in_date = models.DateField(auto_now=True, verbose_name='入库日期')
    items_count = models.IntegerField(default=0, verbose_name='物品数量')
    warehouse = TenantForeignKey(Warehouse, on_delete=models.PROTECT, verbose_name='入库仓库')
    total_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总入库成本') #和供应商谈的加
    actual_total_cost = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际总入库成本（已扣减现金折扣）') # 实际总入库成本
    deposit_deduction = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已抵扣预付款')
    unpaid_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='未付款金额')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='现金折扣')
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, null=True, blank=True, verbose_name='付款方式', related_name='purchase_in_payments')
    pay_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已付款金额')
    allocated_cost = TenantForeignKey('CostAllocation', on_delete=models.PROTECT, null=True, blank=True, verbose_name='均摊费用')
    allocated_payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, null=True, blank=True, verbose_name='均摊付款方式', related_name='purchase_in_allocated_payments')
    allocated_pay_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='均摊已付款金额')

    class Meta:
        verbose_name = '采购入库'
        verbose_name_plural = '采购入库'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"PI-{self.id}"


class PurchaseInItem(LedgerModel):
    """采购入库物品表"""
    id = models.AutoField(primary_key=True)
    purchase_in = TenantForeignKey(PurchaseIn, on_delete=models.CASCADE, verbose_name='采购入库单',
                                    related_name='items')
    purchase_order_item = TenantForeignKey(PurchaseOrderItem, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='采购订单物品')
    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')
    unit = TenantForeignKey(Unit, on_delete=models.PROTECT, verbose_name='计量单位')
    batch_number = models.CharField(max_length=50, null=True, blank=True, verbose_name='批次号' )
    track_id = models.IntegerField(default=0, verbose_name='跟踪ID')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    production_date = models.DateField(null=True, blank=True, verbose_name='生产日期')
    expiry_days = models.IntegerField(default=0, verbose_name='有效天数')
    purchase_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='采购价格') 
    actual_purchase_price = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际采购价')
    allocated_cost = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='均摊费用') # 要被均摊的总费用，快递费这些
    actual_allocated_cost = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际均摊费用') # 实际均摊费用

    class Meta:
        verbose_name = '采购入库物品'
        verbose_name_plural = '采购入库物品'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.purchase_in} - {self.item.name}"


class PurchasePayment(LedgerModel):
    """采购付款单
    采购付款单可以只关联采购订单（付款），
    也可以只关联采购入库（分批付款），也可以都不关联（如独立付款单）
    也可以都关联（如先预付后分批结算）。
    payment_type 为定金时，purchase_in 为空，purchase_order 不为空
    payment_type 为付款时，purchase_in 不为空，purchase_order 为空
    payment_type 为付款时，purchase_in 不为空，purchase_order 为空
    """

    PAYMENT_TYPE_CHOICES = [
        ('payment', '付款'),
        ('deposit', '定金'),
    ]

    id = models.AutoField(primary_key=True)
    supplier = TenantForeignKey('Supplier', on_delete=models.PROTECT, verbose_name='供应商')
    purchase_order = TenantForeignKey('PurchaseOrder', on_delete=models.PROTECT, null=True, blank=True,
                                       verbose_name='采购订单')
    purchase_in = TenantForeignKey('PurchaseIn', on_delete=models.PROTECT, null=True, blank=True,
                                    verbose_name='采购入库')
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='付款金额')
    handler = models.CharField(max_length=50, verbose_name='经办人')
    payment_date = models.DateField(default=timezone.now, verbose_name='付款日期')
    payment_type = models.CharField(max_length=10, choices=PAYMENT_TYPE_CHOICES, default='payment',
                                    verbose_name='付款类型')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '采购付款单'
        verbose_name_plural = '采购付款单'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"PP-{self.id}"


class StockReturn(LedgerModel):
    """库存退货表 - 退回给供应商"""
    RETURN_STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    class ReturnStatusChoices(models.TextChoices):
        DRAFT = 'draft', '暂存'
        COMPLETED = 'completed', '已退货'
        CANCELLED = 'cancelled', '已取消'

    id = models.AutoField(primary_key=True)
    order_id = models.CharField(max_length=50, verbose_name='订单编号')
    supplier = TenantForeignKey('Supplier', on_delete=models.PROTECT, verbose_name='供应商')
    warehouse = TenantForeignKey('Warehouse', on_delete=models.PROTECT, verbose_name='出库仓库')
    return_date = models.DateField(default=timezone.now, verbose_name='退货日期')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='stock_returns_handler')
    return_reason = models.CharField(max_length=200, blank=True, null=True, verbose_name='退货原因')
    purchase_in = TenantForeignKey('PurchaseIn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联采购入库')
    return_status = models.CharField(max_length=20, choices=ReturnStatusChoices.choices, default='draft',verbose_name='退货状态')
    total_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='退货总金额')
    actual_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='实际退款金额')
    refunded_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已退款金额')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='折扣金额')
    items_count = models.IntegerField(default=0, verbose_name='退货物品数量')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    short_desc = models.CharField(max_length=50, verbose_name='简要描述', null=True, blank=True)
    payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, null=True, blank=True, verbose_name='付款方式', related_name='stock_returns_payments')
    pay_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已付款金额')
    allocated_cost = TenantForeignKey('CostAllocation', on_delete=models.PROTECT, null=True, blank=True, verbose_name='均摊费用')
    allocated_payment_method = TenantForeignKey(PaymentMethod, on_delete=models.PROTECT, null=True, blank=True, verbose_name='均摊付款方式', related_name='stock_returns_allocated_payments')
    allocated_pay_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='均摊已付款金额')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '库存退货单'
        verbose_name_plural = '库存退货单'
        ordering = ['-return_date']
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"退货单-{self.id}-{self.supplier.name}"


class StockReturnItem(LedgerModel):
    """库存退货物品表"""
    id = models.AutoField(primary_key=True)
    stock_return = TenantForeignKey('StockReturn', on_delete=models.CASCADE, related_name='items',
                                     verbose_name='退货单')
    stock = TenantForeignKey(Stock, on_delete=models.PROTECT, verbose_name='库存')
    purchase_in_item = TenantForeignKey('PurchaseInItem', on_delete=models.SET_NULL, null=True, blank=True,
                                         verbose_name='关联入库物品')
    track_id = models.IntegerField(default=0, verbose_name='跟踪ID')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='退货数量')
    unit = TenantForeignKey('Unit', on_delete=models.PROTECT, verbose_name='计量单位')
    return_price = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='退货单价')
    is_defective = models.BooleanField(default=False, verbose_name='是否次品')
    defect_description = models.CharField(max_length=200, blank=True, null=True, verbose_name='次品描述')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')
    batch_number = models.CharField(max_length=50, verbose_name='批次号')

    class Meta:
        verbose_name = '退货物品'
        verbose_name_plural = '退货物品'
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.stock_return.id}-{self.stock.item.name}"

    def save(self, *args, **kwargs):
        # 自动计算退货金额
        self.return_amount = self.quantity * self.return_price
        super().save(*args, **kwargs)

class StockReturnPayment(LedgerModel):
    """退货付款记录表"""
    PAYMENT_TYPE_CHOICES = [
        ('refund', '退款'),
        ('credit', '记账抵扣'),
        ('exchange', '换货'),
    ]

    id = models.AutoField(primary_key=True)
    stock_return = TenantForeignKey('StockReturn', on_delete=models.CASCADE, related_name='payments',
                                     verbose_name='退货单')
    amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='退款金额')
    payment_date = models.DateField(default=timezone.now, verbose_name='退款日期')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES, default='refund',
                                    verbose_name='退款类型')
    handler = models.CharField(max_length=50, verbose_name='经办人')
    transaction_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='交易编号')
    remark = models.CharField(max_length=200, blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '退货付款记录'
        verbose_name_plural = '退货付款记录'

    def __str__(self):
        return f"{self.stock_return}-{self.amount}"

class CostAllocation(LedgerModel):
    """费用分摊表"""
    class SourceTypeChoices(models.TextChoices):
        PURCHASE_IN = 'purchase_in', '采购入库'
        SALES_OUT = 'sales_out', '销售出库'

    class AllocationMethodChoices(models.TextChoices):
        AMOUNT = 'amount', '金额分摊'
        QUANTITY = 'quantity', '数量分摊'

    class CostTypeChoices(models.TextChoices):
        SHIPPING = 'shipping', '运费'
        LOADING = 'loading', '装卸费'
        OTHER = 'other', '其他'

    id = models.AutoField(primary_key=True)
    supplier = TenantForeignKey('Supplier', on_delete=models.PROTECT, verbose_name='供应商')
    source_type = models.CharField(max_length=20, choices=SourceTypeChoices, verbose_name='分摊来源')
    source_order_id = models.CharField(max_length=50, verbose_name='订单号', null=True, blank=True)
    order_id = models.CharField(max_length=50, verbose_name='订单号' )
    allocation_method = models.CharField(max_length=20, choices=AllocationMethodChoices, verbose_name='均摊方式')
    total_cost = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='总均摊费用')
    paid_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已付款')
    discount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='现金折扣')

    
    # 使用JSONField存储多个均摊费用和类别的组合
    allocation_details = models.JSONField(default=dict, verbose_name='均摊费用明细', help_text='''
    格式示例：[
        {"cost": "100.00", "type": "shipping"},
        {"cost": "50.00", "type": "other"},
        {"cost": "30.00", "type": "shipping"}
    ]
    ''')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    handler = TenantForeignKey(ZTUser, on_delete=models.PROTECT, verbose_name='经办人', related_name='cost_allocations_handler')
    remark = models.CharField(max_length=200, null=True, blank=True, verbose_name='备注')

    class Meta:
        verbose_name = '费用分摊'
        verbose_name_plural = '费用分摊'
        ordering = ['-created_at']
        unique_together = ('id', 'ledger')

    def __str__(self):
        source = self.source_order_id;
        return f"费用分摊-{self.source_type}-{source}"

    def clean(self):
        if self.source_type == 'purchase_in' and not self.purchase_in:
            raise WmsException(_T('采购入库分摊必须关联采购入库单'))
        if self.source_type == 'sales_out' and not self.sales_out:
            raise WmsException(_T('销售出库分摊必须关联销售出库单'))
        
        # 验证allocation_details的格式
        if not isinstance(self.allocation_details, dict):
            raise WmsException(_T('均摊费用明细格式错误'))
        
        for key, value in self.allocation_details.items():
            if not isinstance(value, dict) or 'amount' not in value or 'type' not in value:
                raise WmsException(_T('均摊费用明细{key}格式错误', key=key))
            if value['type'] not in [choice[0] for choice in self.CostTypeChoices.choices]:
                raise WmsException(_T('均摊费用类别{type}不合法', type=value['type']))
            try:
                float(value['amount'])
            except (TypeError, ValueError):
                raise WmsException(_T('均摊费用金额{amount}必须是数字', amount=value['amount']))


class StockHistory(LedgerModel):
    """库存入库历史表"""
    class HistoryTypeChoices(models.TextChoices):
        PURCHASE_IN = 'pi', '采购入库'
        SALES_OUT = 'so', '销售出库'
        ADJUSTMENT_IN = 'ai', '调拨入库'
        ADJUSTMENT_OUT = 'ao', '调拨出库'
        ASSEMBLY_IN = 'ami', '组装入库'
        ASSEMBLY_OUT = 'amo', '组装出库'
        REPAIR_IN = 'rpi', '维修入库'
        REPAIR_OUT = 'rpo', '维修出库'
        RETURN_IN = 'rin', '退货入库'
        RETURN_OUT = 'rot', '退货出库'
        STOCK_RETURN = 'srt', '库存退货'
        INITIAL_IN='ini', '初始入库'

    stock = TenantForeignKey(Stock, on_delete=models.PROTECT, verbose_name='库存')
    track_id = models.IntegerField(verbose_name='跟踪ID')
    history_date = models.DateField(auto_now_add=True, verbose_name='历史日期')
    history_type = models.CharField(max_length=20, choices=HistoryTypeChoices, verbose_name='历史类型')
    history_order_id = models.CharField(max_length=50, verbose_name='历史订单号')
    quantity = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='数量')
    demand_item_keyid = models.IntegerField(verbose_name='外链订单的物品主键ID')

    item = TenantForeignKey(Item, on_delete=models.PROTECT, verbose_name='物品')

    class Meta:
        verbose_name = '库存历史'
        verbose_name_plural = '库存历史'
        ordering = ['-history_date']
        unique_together = ('id', 'ledger')

    def __str__(self):
        return f"{self.stock.item.name} {self.history_type} {self.history_date}"

class AccountsPayable(LedgerModel):
    """应付账本表"""
    class SourceTypeChoices(models.TextChoices):
        PURCHASE_PAYABLE = 'purchase_payable', '进货应付'
        RETURN_PAYABLE = 'return_payable', '退货应付'
        COST_ALLOCATION_PAYABLE = 'cost_allocation_payable', '分摊费用应付'

    class PaymentStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', '未付款'
        PARTIAL = 'partial', '部分付款'
        PAID = 'paid', '已付款'

    payable_no = models.CharField(max_length=50, verbose_name='应付单号', unique=True) # 自身标识
    source_order_no = models.CharField(max_length=50, verbose_name='来源单号')
    source_type = models.CharField(max_length=30, choices=SourceTypeChoices, verbose_name='来源类型')
    supplier = TenantForeignKey('Supplier', on_delete=models.PROTECT, verbose_name='供应商', related_name='payables_supplier',null=True, blank=True)
    # 销售退货顾客
    customer = TenantForeignKey('Customer', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='顾客', related_name='payables_customer')
    
    # 金额相关字段
    order_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='订单总金额')
    payable_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='应付金额')
    remaining_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='欠款金额')
    
    # 状态和时间
    payment_status = models.CharField(max_length=20, choices=PaymentStatusChoices.choices, default=PaymentStatusChoices.UNPAID, verbose_name='付款状态')
    due_date = models.DateField(null=True, blank=True, verbose_name='到期日期')
    create_date = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_date = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    # 关联字段
    purchase_order = TenantForeignKey('PurchaseOrder', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联采购订单')
    purchase_in = TenantForeignKey('PurchaseIn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联采购入库')
    stock_return = TenantForeignKey('StockReturn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联库存退货')
    sales_return = TenantForeignKey('SalesReturn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联销售退货')
    cost_allocation = TenantForeignKey('CostAllocation', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联费用分摊')
    
    # 经办人和备注
    handler = TenantForeignKey('ZTUser', on_delete=models.PROTECT, verbose_name='经办人', related_name='accounts_payable_handler')
    remark = models.CharField(max_length=500, blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '应付账本'
        verbose_name_plural = '应付账本'
        ordering = ['-create_date']
        unique_together = ('id', 'ledger')
        indexes = [
            models.Index(fields=['payable_no'], name='ap_no_idx'),
            models.Index(fields=['supplier'], name='ap_supplier_idx'),
            models.Index(fields=['source_order_no'], name='ap_source_order_idx'),
            models.Index(fields=['payment_status'], name='ap_status_idx'),
            models.Index(fields=['due_date'], name='ap_due_date_idx'),
        ]

    def __str__(self):
        return f"{self.payable_no} - {self.supplier.name} - {self.get_source_type_display()}"

class AccountsReceivable(LedgerModel):
    """应收账本表"""
    class SourceTypeChoices(models.TextChoices):
        SALES_RECEIVABLE = 'sales_receivable', '销售应收'
        RETURN_RECEIVABLE = 'return_receivable', '退货应收'
        OTHER_RECEIVABLE = 'other_receivable', '其他应收'

    class PaymentStatusChoices(models.TextChoices):
        UNPAID = 'unpaid', '未收款'
        PARTIAL = 'partial', '部分收款'
        PAID = 'paid', '已收款'

    id = models.AutoField(primary_key=True)
    receivable_no = models.CharField(max_length=50, verbose_name='应收单号', unique=True) # 自身标识
    source_order_no = models.CharField(max_length=50, verbose_name='来源单号')
    source_type = models.CharField(max_length=30, choices=SourceTypeChoices, verbose_name='来源类型')
    customer = TenantForeignKey('Customer', on_delete=models.PROTECT, verbose_name='客户')
    
    # 金额相关字段
    order_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='订单总金额')
    receivable_amount = models.DecimalField(max_digits=30, decimal_places=8, verbose_name='应收金额')
    received_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='已收款金额')
    remaining_amount = models.DecimalField(max_digits=30, decimal_places=8, default=0, verbose_name='欠款金额', null=True, blank=True)
    
    # 状态和时间
    payment_status = models.CharField(max_length=20, choices=PaymentStatusChoices, default=PaymentStatusChoices.UNPAID, verbose_name='收款状态')
    due_date = models.DateField(null=True, blank=True, verbose_name='到期日期')
    create_date = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    update_date = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    # 关联字段
    sales_order = TenantForeignKey('SalesOrder', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联销售订单')
    sales_out = TenantForeignKey('SalesOut', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联销售出库')
    sales_return = TenantForeignKey('SalesReturn', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联销售退货')
    
    # 经办人和备注
    handler = TenantForeignKey('ZTUser', on_delete=models.PROTECT, verbose_name='经办人', related_name='accounts_receivable_handler')
    # controller = TenantForeignKey('ZTUser', on_delete=models.PROTECT, verbose_name='操作者', related_name='accounts_receivable_controller')
    remark = models.CharField(max_length=500, blank=True, null=True, verbose_name='备注')

    class Meta:
        verbose_name = '应收账本'
        verbose_name_plural = '应收账本'
        ordering = ['-create_date']
        unique_together = ('id', 'ledger')
        indexes = [
            models.Index(fields=['receivable_no'], name='ar_no_idx'),
            models.Index(fields=['customer'], name='ar_customer_idx'),
            models.Index(fields=['source_order_no'], name='ar_source_order_idx'),
            models.Index(fields=['payment_status'], name='ar_status_idx'),
            models.Index(fields=['due_date'], name='ar_due_date_idx'),
        ]

    def __str__(self):
        return f"{self.receivable_no} - {self.customer.name} - {self.get_source_type_display()}"
